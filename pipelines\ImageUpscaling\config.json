{"name": "ImageUpscaling", "description": "Image upscaling using RealESRGAN, SwinIR, and other upscaling models", "dependencies": {"python": ["torch>=2.0.0", "torchvision>=0.15.0", "pillow>=10.0.0", "numpy>=1.24.0", "opencv-python>=4.8.0", "basicsr>=1.4.2", "realesrgan>=0.3.0", "facexlib>=0.2.5", "gfpgan>=1.3.8", "scikit-image>=0.20.0", "imageio>=2.30.0", "tifffile>=2023.0.0"], "models": [{"name": "realesrgan-x4plus", "repo_id": "xinntao/Real-ESRGAN", "required": true, "description": "RealESRGAN x4Plus - General purpose upscaler", "local_path": "ImageUpscaling/realesrgan-x4plus"}, {"name": "4xlsdirplus", "repo_id": "xinntao/4x-LSDIRplus", "required": false, "description": "4xLSDIRplus - Lightweight upscaler", "local_path": "ImageUpscaling/4xlsdirplus"}, {"name": "swinir-m-x4", "repo_id": "JingyunLiang/SwinIR", "required": false, "description": "SwinIR-M-x4 - SwinIR medium 4x upscaler", "local_path": "ImageUpscaling/swinir-m-x4"}, {"name": "4xffhqdat", "repo_id": "xinntao/4x_FFHQDAT", "required": false, "description": "4xFFHQDAT - Face upscaler", "local_path": "ImageUpscaling/4xffhqdat"}]}}