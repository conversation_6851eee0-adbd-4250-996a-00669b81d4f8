from __future__ import annotations

from abc import ABC
from pathlib import Path

from virtualenv.create.describe import PosixSupports, WindowsSupports
from virtualenv.create.via_global_ref.builtin.ref import PathRefToDest, RefMust, RefWhen
from virtualenv.create.via_global_ref.builtin.via_global_self_do import ViaGlobalRefVirtualenvBuiltin


class GraalPy(ViaGlobalRefVirtualenvBuiltin, ABC):
    @classmethod
    def can_describe(cls, interpreter):
        return interpreter.implementation == "GraalVM" and super().can_describe(interpreter)

    @classmethod
    def exe_stem(cls):
        return "graalpy"

    @classmethod
    def exe_names(cls, interpreter):
        return {
            cls.exe_stem(),
            "python",
            f"python{interpreter.version_info.major}",
            f"python{interpreter.version_info.major}.{interpreter.version_info.minor}",
        }

    @classmethod
    def _executables(cls, interpreter):
        host = Path(interpreter.system_executable)
        targets = sorted(f"{name}{cls.suffix}" for name in cls.exe_names(interpreter))
        yield host, targets, RefMust.NA, RefWhen.ANY

    @classmethod
    def sources(cls, interpreter):
        yield from super().sources(interpreter)
        python_dir = Path(interpreter.system_executable).resolve().parent
        if python_dir.name in {"bin", "Scripts"}:
            python_dir = python_dir.parent

        native_lib = cls._native_lib(python_dir / "lib", interpreter.platform)
        if native_lib.exists():
            yield PathRefToDest(native_lib, dest=lambda self, s: self.bin_dir.parent / "lib" / s.name)

        for jvm_dir_name in ("jvm", "jvmlibs", "modules"):
            jvm_dir = python_dir / jvm_dir_name
            if jvm_dir.exists():
                yield PathRefToDest(jvm_dir, dest=lambda self, s: self.bin_dir.parent / s.name)

    @classmethod
    def _shared_libs(cls, python_dir):
        raise NotImplementedError

    def set_pyenv_cfg(self):
        super().set_pyenv_cfg()
        # GraalPy 24.0 and older had home without the bin
        version = self.interpreter.version_info
        if version.major == 3 and version.minor <= 10:  # noqa: PLR2004
            home = Path(self.pyenv_cfg["home"])
            if home.name == "bin":
                self.pyenv_cfg["home"] = str(home.parent)


class GraalPyPosix(GraalPy, PosixSupports):
    @classmethod
    def _native_lib(cls, lib_dir, platform):
        if platform == "darwin":
            return lib_dir / "libpythonvm.dylib"
        return lib_dir / "libpythonvm.so"


class GraalPyWindows(GraalPy, WindowsSupports):
    @classmethod
    def _native_lib(cls, lib_dir, _platform):
        return lib_dir / "pythonvm.dll"

    def set_pyenv_cfg(self):
        # GraalPy needs an additional entry in pyvenv.cfg on Windows
        super().set_pyenv_cfg()
        self.pyenv_cfg["venvlauncher_command"] = self.interpreter.system_executable


__all__ = [
    "GraalPyPosix",
    "GraalPyWindows",
]
