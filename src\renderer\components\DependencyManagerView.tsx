import React, { useState, useEffect } from 'react';
import { Download, CheckCircle, XCircle, HelpCircle, RefreshCw, HardDrive, Package, ChevronDown, ChevronRight, Cpu, Image, Layers, AlertTriangle, Clock } from 'lucide-react';

// Error boundary for dependency manager
class DependencyErrorBoundary extends React.Component<
  React.PropsWithChildren<{}>, 
  { hasError: boolean; error: any }
> {
  constructor(props: React.PropsWithChildren<{}>) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: any) {
    console.error('🔍 DependencyErrorBoundary: Error caught:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error('🔍 DependencyErrorBoundary caught an error:', error);
    console.error('🔍 DependencyErrorBoundary error info:', errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-8 text-center">
          <div className="text-red-600 mb-4">⚠️ Dependency Manager Error</div>
          <div className="text-sm text-red-500 mb-4">
            {this.state.error?.message || 'Unknown error occurred while loading dependencies'}
          </div>
          <button 
            onClick={() => window.location.reload()} 
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Reload App
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

type DependencyDetail = {
  satisfied: boolean;
  installed: string;
  required: string;
};

type ModelDetail = {
  name: string;
  repo_id: string;
  installed: boolean;
  required: boolean;
};

type PipelineStatus = {
  name: string;
  description: string;
  python: {
    installed: boolean;
    details: { [key: string]: DependencyDetail };
    dependencies: string[];
  };
  models: {
    installed: boolean;
    details: { [key: string]: ModelDetail };
    dependencies: any[];
  };
};

type InstallationProgress = {
  status: 'Running' | 'Complete' | 'Error';
  message: string;
  progress: number;
};

type InstallationUpdate = {
  pipeline: string;
  component: 'python' | 'models';
  name: string;
  status: 'Running' | 'Complete' | 'Error';
  message: string;
  progress: number;
};

interface DependencyManagerViewProps {
  isDarkMode: boolean;
}

const DependencyManagerViewInner: React.FC<DependencyManagerViewProps> = ({ isDarkMode }) => {
  const [status, setStatus] = useState<PipelineStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [installing, setInstalling] = useState<{ [key: string]: InstallationProgress }>({});
  const [progressPercent, setProgressPercent] = useState(0);
  const [expandedModules, setExpandedModules] = useState<{ [key: string]: boolean }>({});
  const [overallHealth, setOverallHealth] = useState<'Good' | 'Poor' | 'Checking'>('Checking');
  const [fullInstallProgress, setFullInstallProgress] = useState<{ progress: number; message: string; active: boolean }>({
    progress: 0,
    message: '',
    active: false
  });
  const [persistentStatus, setPersistentStatus] = useState<{ [key: string]: { status: 'satisfied' | 'attention' | 'checking'; message: string; timestamp: number } }>({});

  const fetchStatus = async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsLoading(true);
      }
      setError(null);
      console.log('🔍 DependencyManagerView: Starting fetchStatus...');
      
      const result = await window.electronAPI.getDependencyStatus();
      console.log('🔍 DependencyManagerView: Raw result:', result);
      
      // Handle wrapped response from IPC
      if (result && typeof result === 'object' && 'success' in result && 'data' in result) {
        console.log('🔍 DependencyManagerView: Processing wrapped response...');
        if (result.success && Array.isArray(result.data)) {
          console.log('🔍 DependencyManagerView: Setting status with data:', result.data);
          setStatus(result.data);
        } else {
          console.error('🔍 DependencyManagerView: Request failed:', (result as any).error);
          throw new Error((result as any).error || 'Request failed');
        }
      } else if (Array.isArray(result)) {
        console.log('🔍 DependencyManagerView: Processing direct array response...');
        setStatus(result);
      } else {
        console.error('🔍 DependencyManagerView: Invalid response format:', result);
        throw new Error('Invalid response format');
      }
      
      console.log('🔍 DependencyManagerView: fetchStatus completed successfully');

      // Check overall health after successful fetch
      if (result && typeof result === 'object' && 'success' in result && 'data' in result && result.success) {
        checkOverallHealth(result.data as PipelineStatus[]);
      } else if (Array.isArray(result)) {
        checkOverallHealth(result as PipelineStatus[]);
      }
    } catch (err) {
      console.error('🔍 DependencyManagerView: Error in fetchStatus:', err);
      if (showLoading) {
        setError('Failed to fetch dependency status.');
      }
    } finally {
      if (showLoading) {
        setIsLoading(false);
      }
    }
  };

  const checkOverallHealth = (statusData: PipelineStatus[]) => {
    // Check core dependencies and basic requirements
    const coreModule = statusData.find(s => s.name === 'Core');
    const imageGenModule = statusData.find(s => s.name === 'ImageGeneration');

    let hasIssues = false;

    // Check if core dependencies are satisfied
    if (!coreModule?.python?.installed || !coreModule?.models?.installed) {
      hasIssues = true;
    }

    // Check if basic image generation is working (required for basic app functionality)
    if (!imageGenModule?.python?.installed) {
      hasIssues = true;
    }

    // Check for any critical errors in other modules
    statusData.forEach(module => {
      if (module.name === 'Core' || module.name === 'ImageGeneration') {
        if (!module.python?.satisfied) {
          hasIssues = true;
        }
      }
    });

    setOverallHealth(hasIssues ? 'Poor' : 'Good');

    // Update persistent status for all pipelines
    updatePersistentStatus(statusData);
  };

  const updatePersistentStatus = (statusData: PipelineStatus[]) => {
    const newPersistentStatus: { [key: string]: { status: 'satisfied' | 'attention' | 'checking'; message: string; timestamp: number } } = {};

    statusData.forEach(pipeline => {
      const timestamp = Date.now();

      // Check Python dependencies status
      const pythonKey = `${pipeline.name}-python-all`;
      if (pipeline.python?.installed) {
        newPersistentStatus[pythonKey] = {
          status: 'satisfied',
          message: '✓ All Python dependencies satisfied',
          timestamp
        };
      } else {
        newPersistentStatus[pythonKey] = {
          status: 'attention',
          message: '⚠ Attention: Missing Python dependencies',
          timestamp
        };
      }

      // Check Models status
      const modelsKey = `${pipeline.name}-models-all`;
      const isSystemPackage = pipeline.name === 'Microsoft_TRELLIS' || pipeline.name === 'Hunyaun3d-2';

      if (isSystemPackage) {
        // For system packages (Trellis/Hunyuan), models are satisfied if Python environment is satisfied
        if (pipeline.python?.installed) {
          newPersistentStatus[modelsKey] = {
            status: 'satisfied',
            message: '✓ Models managed by system package',
            timestamp
          };
        } else {
          newPersistentStatus[modelsKey] = {
            status: 'attention',
            message: '⚠ Attention: System package not installed',
            timestamp
          };
        }
      } else {
        // For regular pipelines, check actual model installation status
        if (pipeline.models?.installed) {
          newPersistentStatus[modelsKey] = {
            status: 'satisfied',
            message: '✓ All models satisfied',
            timestamp
          };
        } else {
          newPersistentStatus[modelsKey] = {
            status: 'attention',
            message: '⚠ Attention: Missing models',
            timestamp
          };
        }
      }

      // Check individual Python dependencies
      if (pipeline.python?.details) {
        Object.entries(pipeline.python.details).forEach(([depName, detail]: [string, any]) => {
          if (detail.required) {
            const depKey = `${pipeline.name}-python-${detail.required}`;
            if (detail.satisfied) {
              newPersistentStatus[depKey] = {
                status: 'satisfied',
                message: `✓ ${detail.required} satisfied`,
                timestamp
              };
            } else {
              newPersistentStatus[depKey] = {
                status: 'attention',
                message: `⚠ Attention: ${detail.required} missing`,
                timestamp
              };
            }
          }
        });
      }

      // Check individual models
      if (pipeline.models?.details) {
        Object.entries(pipeline.models.details).forEach(([modelName, model]: [string, any]) => {
          const modelKey = `${pipeline.name}-models-${model.name}`;
          if (model.installed) {
            newPersistentStatus[modelKey] = {
              status: 'satisfied',
              message: `✓ ${model.name} installed`,
              timestamp
            };
          } else {
            newPersistentStatus[modelKey] = {
              status: 'attention',
              message: `⚠ Attention: ${model.name} missing`,
              timestamp
            };
          }
        });
      }
    });

    setPersistentStatus(newPersistentStatus);
  };

  const getModuleOverallStatus = (module: any) => {
    let totalSatisfied = 0;
    let totalItems = 0;
    let hasAttention = false;

    module.pipelines.forEach((pipeline: PipelineStatus) => {
      const isSystemPackage = pipeline.name === 'Microsoft_TRELLIS' || pipeline.name === 'Hunyaun3d-2';

      // Check Python dependencies
      if (pipeline.python) {
        totalItems++;
        if (pipeline.python.installed) {
          totalSatisfied++;
        } else {
          hasAttention = true;
        }
      }

      // Check Models (ignore for Core module)
      if (pipeline.models && module.id !== 'core') {
        totalItems++;

        if (isSystemPackage) {
          // For system packages, models are satisfied if Python environment is satisfied
          if (pipeline.python?.installed) {
            totalSatisfied++;
          } else {
            hasAttention = true;
          }
        } else {
          // For regular pipelines, check actual model installation status
          if (pipeline.models.installed) {
            totalSatisfied++;
          } else {
            hasAttention = true;
          }
        }
      }
    });

    if (totalItems === 0) {
      return { status: 'unknown', message: 'No dependencies found', color: 'gray' };
    }

    if (totalSatisfied === totalItems) {
      return {
        status: 'satisfied',
        message: '✓ All dependencies satisfied',
        color: 'green'
      };
    } else if (hasAttention) {
      return {
        status: 'attention',
        message: `⚠ ${totalItems - totalSatisfied} of ${totalItems} need attention`,
        color: 'yellow'
      };
    } else {
      return {
        status: 'partial',
        message: `${totalSatisfied}/${totalItems} satisfied`,
        color: 'blue'
      };
    }
  };

  const repairRequiredDependencies = async () => {
    try {
      setFullInstallProgress({ progress: 0, message: 'Checking required dependencies...', active: true });

      // Get current status first
      const currentStatus = await window.electronAPI.getDependencyStatus();
      let statusData: PipelineStatus[] = [];

      if (currentStatus && typeof currentStatus === 'object' && 'success' in currentStatus && 'data' in currentStatus) {
        statusData = currentStatus.success ? (currentStatus.data as PipelineStatus[]) : [];
      } else if (Array.isArray(currentStatus)) {
        statusData = currentStatus;
      }

      const coreModule = statusData.find(s => s.name === 'Core');
      const imageGenModule = statusData.find(s => s.name === 'ImageGeneration');

      let repairNeeded = false;

      // Check Core dependencies
      if (!coreModule?.python?.installed) {
        repairNeeded = true;
        setFullInstallProgress({ progress: 25, message: 'Repairing Core dependencies...', active: true });
        await window.electronAPI.installDependencies('Core', 'python', 'all');
      } else {
        setFullInstallProgress({ progress: 25, message: 'Core dependencies are already satisfied', active: true });
      }

      // Check ImageGeneration dependencies
      if (!imageGenModule?.python?.installed) {
        repairNeeded = true;
        setFullInstallProgress({ progress: 75, message: 'Repairing Image Generation dependencies...', active: true });
        await window.electronAPI.installDependencies('ImageGeneration', 'python', 'all');
      } else {
        setFullInstallProgress({ progress: 75, message: 'Image Generation dependencies are already satisfied', active: true });
      }

      const finalMessage = repairNeeded
        ? 'Required dependencies repaired successfully!'
        : 'All required dependencies are already satisfied!';

      setFullInstallProgress({ progress: 100, message: finalMessage, active: true });

      // Refresh status
      await fetchStatus(false);

      setTimeout(() => {
        setFullInstallProgress({ progress: 0, message: '', active: false });
      }, 3000);
    } catch (error) {
      console.error('Failed to repair dependencies:', error);
      setFullInstallProgress({ progress: 0, message: 'Failed to repair dependencies', active: false });
    }
  };

  const installFullDependencies = async () => {
    try {
      setFullInstallProgress({ progress: 0, message: 'Checking all dependencies...', active: true });

      // Get current status first
      const currentStatus = await window.electronAPI.getDependencyStatus();
      let statusData: PipelineStatus[] = [];

      if (currentStatus && typeof currentStatus === 'object' && 'success' in currentStatus && 'data' in currentStatus) {
        statusData = currentStatus.success ? (currentStatus.data as PipelineStatus[]) : [];
      } else if (Array.isArray(currentStatus)) {
        statusData = currentStatus;
      }

      const modules = ['Core', 'ImageGeneration', 'ImageUpscaling', 'Microsoft_TRELLIS', 'Hunyaun3d-2'];
      const totalModules = modules.length;
      let installationsPerformed = 0;

      for (let i = 0; i < modules.length; i++) {
        const module = modules[i];
        const baseProgress = (i / totalModules) * 100;
        const moduleProgress = 100 / totalModules;

        const pipeline = statusData.find(p => p.name === module);
        if (!pipeline) {
          setFullInstallProgress({
            progress: baseProgress + moduleProgress,
            message: `Skipping ${module} (not found)...`,
            active: true
          });
          continue;
        }

        // Check Python dependencies
        if (!pipeline.python?.installed) {
          setFullInstallProgress({
            progress: baseProgress + (moduleProgress * 0.5),
            message: `Installing ${module} Python dependencies...`,
            active: true
          });
          await window.electronAPI.installDependencies(module, 'python', 'all');
          installationsPerformed++;
        } else {
          setFullInstallProgress({
            progress: baseProgress + (moduleProgress * 0.5),
            message: `${module} Python dependencies already satisfied`,
            active: true
          });
        }

        // Check Models
        if (!pipeline.models?.installed) {
          setFullInstallProgress({
            progress: baseProgress + moduleProgress,
            message: `Installing ${module} models...`,
            active: true
          });
          await window.electronAPI.installDependencies(module, 'models', 'all');
          installationsPerformed++;
        } else {
          setFullInstallProgress({
            progress: baseProgress + moduleProgress,
            message: `${module} models already satisfied`,
            active: true
          });
        }
      }

      const finalMessage = installationsPerformed > 0
        ? `All dependencies installed successfully! (${installationsPerformed} installations performed)`
        : 'All dependencies were already satisfied!';

      setFullInstallProgress({ progress: 100, message: finalMessage, active: true });

      // Refresh status
      await fetchStatus(false);

      setTimeout(() => {
        setFullInstallProgress({ progress: 0, message: '', active: false });
      }, 4000);
    } catch (error) {
      console.error('Failed to install full dependencies:', error);
      setFullInstallProgress({ progress: 0, message: 'Failed to install full dependencies', active: false });
    }
  };

  // Background status update that doesn't show loading spinner
  const updateStatusSilently = async () => {
    await fetchStatus(false);
  };

  const handleRefresh = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('🔄 Manual refresh initiated by user');
      
      // Clear any stale installation states when manually refreshing
      setInstalling({});
      
      const result = await window.electronAPI.refreshPipelineTemplates();
      setStatus(result);
      
      console.log('🔄 Manual refresh completed');
    } catch (err) {
      console.error('Error refreshing pipeline templates:', err);
      setError('Failed to refresh pipeline templates.');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // reset progress when loading starts
    setProgressPercent(0);

    fetchStatus(); // Initial load shows loading indicator

    const removeInstallListener = window.electronAPI.onInstallationProgress((update: InstallationUpdate) => {
      const key = `${update.pipeline}-${update.component}-${update.name || 'all'}`;
      setInstalling(prev => ({
        ...prev,
        [key]: {
          status: update.status,
          message: update.message,
          progress: update.progress,
        }
      }));

      // If installation is complete or failed, update persistent status and clear installation state
      if (update.status === 'Complete' || update.status === 'Error') {
        // Update persistent status
        if (update.status === 'Complete') {
          setPersistentStatus(prev => ({
            ...prev,
            [key]: {
              status: 'satisfied',
              message: `✓ ${update.message}`,
              timestamp: Date.now()
            }
          }));
        } else if (update.status === 'Error') {
          setPersistentStatus(prev => ({
            ...prev,
            [key]: {
              status: 'attention',
              message: `⚠ Error: ${update.message}`,
              timestamp: Date.now()
            }
          }));
        }

        // Update the installation state to show completion for a moment
        setInstalling(prev => ({
          ...prev,
          [key]: {
            status: update.status,
            message: update.message,
            progress: 100,
          }
        }));

        // Wait a moment for filesystem to update, then silently refresh status
        setTimeout(() => {
          updateStatusSilently();
        }, 2000);

        // Clear the installation state after showing completion message
        setTimeout(() => {
          setInstalling(prev => {
            const newState = { ...prev };
            delete newState[key];
            
            // If this was an "Install All" models operation, also clear any individual model states
            if (update.name === 'all' && update.component === 'models') {
              Object.keys(newState).forEach(stateKey => {
                if (stateKey.startsWith(`${update.pipeline}-models-`) && stateKey !== key) {
                  delete newState[stateKey];
                }
              });
            }
            
            return newState;
          });
        }, 4000); // Show completion message for 4 seconds before clearing
      }
    });

    const removeStatusListener = (window as any).electronAPI.onStatusProgress((data: {percent: number}) => {
      setProgressPercent(data.percent);
    });

    return () => {
      removeInstallListener();
      removeStatusListener();
    };
  }, []);

  const handleInstall = async (pipelineName: string, component: 'python' | 'models', name?: string) => {
    const key = `${pipelineName}-${component}-${name || 'all'}`;

    // First, check if installation is actually needed
    setInstalling(prev => ({
      ...prev,
      [key]: { status: 'Running', message: 'Checking current status...', progress: 5 }
    }));

    try {
      // Get current status to check if anything is actually missing
      const currentStatus = await window.electronAPI.getDependencyStatus();
      let statusData: PipelineStatus[] = [];

      if (currentStatus && typeof currentStatus === 'object' && 'success' in currentStatus && 'data' in currentStatus) {
        statusData = currentStatus.success ? (currentStatus.data as PipelineStatus[]) : [];
      } else if (Array.isArray(currentStatus)) {
        statusData = currentStatus as PipelineStatus[];
      }

      const pipeline = statusData.find(p => p.name === pipelineName);
      if (!pipeline) {
        throw new Error(`Pipeline ${pipelineName} not found`);
      }

      let needsInstallation = false;
      let skipReason = '';

      if (component === 'python') {
        if (name && name !== 'all') {
          // Check specific Python dependency
          const pythonDetails = pipeline.python?.details || {};
          const dependency = Object.values(pythonDetails).find((dep: any) =>
            dep.required === name || dep.required?.includes?.(name)
          );
          needsInstallation = !dependency?.satisfied;
          skipReason = dependency?.satisfied ? `${name} is already installed` : '';
        } else {
          // Check all Python dependencies
          needsInstallation = !pipeline.python?.installed;
          skipReason = pipeline.python?.installed ? 'All Python dependencies are already installed' : '';
        }
      } else if (component === 'models') {
        if (name && name !== 'all') {
          // Check specific model
          const modelDetails = pipeline.models?.details || {};
          const model = Object.values(modelDetails).find((m: any) => m.name === name);
          needsInstallation = !model?.installed;
          skipReason = model?.installed ? `${name} model is already installed` : '';
        } else {
          // Check all models
          needsInstallation = !pipeline.models?.installed;
          skipReason = pipeline.models?.installed ? 'All models are already installed' : '';
        }
      }

      if (!needsInstallation) {
        // Nothing to install, update persistent status and clear installing state
        setPersistentStatus(prev => ({
          ...prev,
          [key]: {
            status: 'satisfied',
            message: `✓ ${skipReason}`,
            timestamp: Date.now()
          }
        }));

        // Clear the installing status immediately since no work was done
        setInstalling(prev => {
          const newState = { ...prev };
          delete newState[key];
          return newState;
        });

        return;
      }

      // Proceed with installation if needed
      setInstalling(prev => ({
        ...prev,
        [key]: { status: 'Running', message: 'Starting installation...', progress: 10 }
      }));

      window.electronAPI.installDependencies(pipelineName, component, name);

    } catch (error) {
      console.error('Error checking installation status:', error);
      // If status check fails, proceed with installation anyway
      setInstalling(prev => ({
        ...prev,
        [key]: { status: 'Running', message: 'Starting installation...', progress: 10 }
      }));

      window.electronAPI.installDependencies(pipelineName, component, name);
    }
  };

  const getPythonStatus = (
    details: { [key: string]: DependencyDetail } | { _summary?: string }
  ): { summary?: string; deps: DependencyDetail[] } => {
    const summary = (details as any)._summary;
    const deps = Object.values(details)
      .filter((v) => typeof v === 'object' && (v as any).required) as DependencyDetail[];
    return { summary, deps };
  };

  // Toggle module expansion
  const toggleModule = (moduleId: string) => {
    setExpandedModules(prev => ({
      ...prev,
      [moduleId]: !prev[moduleId]
    }));
  };

  // Get module configuration based on pipeline data
  const getModuleConfig = () => {
    const modules = [];

    // Core Dependencies Module
    const coreDeps = status.filter(p => p.name === 'Core' || p.name.includes('Core'));
    if (coreDeps.length > 0) {
      modules.push({
        id: 'core',
        name: 'Core Dependencies',
        description: 'Essential system dependencies and Python packages',
        icon: <Package className="w-5 h-5" />,
        color: 'green',
        pipelines: coreDeps
      });
    }

    // Trellis Module
    const trellisPipelines = status.filter(p =>
      p.name.toLowerCase().includes('trellis') ||
      p.name === 'Microsoft_TRELLIS'
    );
    if (trellisPipelines.length > 0) {
      modules.push({
        id: 'trellis',
        name: 'Trellis Module',
        description: 'Microsoft TRELLIS 3D generation pipeline',
        icon: <Layers className="w-5 h-5" />,
        color: 'purple',
        pipelines: trellisPipelines
      });
    }

    // Hunyuan3D-2.0 Module
    const hunyuanPipelines = status.filter(p => {
      const name = p.name.toLowerCase();
      return name.includes('hunyuan') ||
             p.name === 'Hunyuan3D-2' ||
             p.name === 'Hunyaun3d-2'; // Handle the typo in the pipeline name
    });
    if (hunyuanPipelines.length > 0) {
      modules.push({
        id: 'hunyuan',
        name: 'Hunyuan3D-2.0 Module',
        description: 'Tencent Hunyuan3D-2.0 3D generation pipeline',
        icon: <Cpu className="w-5 h-5" />,
        color: 'red',
        pipelines: hunyuanPipelines
      });
    }

    // Image Generation Module
    const imagePipelines = status.filter(p => {
      const name = p.name.toLowerCase();
      return (name.includes('image') ||
              name.includes('sdxl') ||
              name.includes('flux') ||
              p.name === 'ImageGeneration') && // Exact match for the main pipeline
              p.name !== 'ImageUpscaling'; // Exclude ImageUpscaling from this module
    });
    if (imagePipelines.length > 0) {
      modules.push({
        id: 'image',
        name: 'Image Generation Module',
        description: 'SDXL, FLUX and other image generation models',
        icon: <Image className="w-5 h-5" />,
        color: 'gray',
        pipelines: imagePipelines
      });
    }

    // Image Upscaling Module
    const upscalingPipelines = status.filter(p => p.name === 'ImageUpscaling');
    if (upscalingPipelines.length > 0) {
      modules.push({
        id: 'upscaling',
        name: 'Image Upscaling Module',
        description: 'RealESRGAN, SwinIR and other image upscaling models',
        icon: <Image className="w-5 h-5" />,
        color: 'pink',
        pipelines: upscalingPipelines
      });
    }

    return modules;
  };

  const renderModule = (module: any) => {
    const isExpanded = expandedModules[module.id];
    const allInstalled = module.pipelines.every((p: PipelineStatus) => p.python.installed && p.models.installed);
    const anyInstalling = module.pipelines.some((p: PipelineStatus) =>
      installing[`${p.name}-python-all`]?.status === 'Running' ||
      installing[`${p.name}-models-all`]?.status === 'Running'
    );

    // Calculate overall module progress
    const calculateModuleProgress = () => {
      if (allInstalled) return 100;

      let totalComponents = 0;
      let completedComponents = 0;
      let activeProgress = 0;
      let activeInstallations = 0;

      module.pipelines.forEach((p: PipelineStatus) => {
        // Count total components (python + models for each pipeline)
        totalComponents += 2;

        // Count completed components
        if (p.python.installed) completedComponents++;
        if (p.models.installed) completedComponents++;

        // Add active installation progress
        const pythonKey = `${p.name}-python-all`;
        const modelsKey = `${p.name}-models-all`;

        if (installing[pythonKey]?.status === 'Running') {
          activeProgress += installing[pythonKey].progress || 0;
          activeInstallations++;
        } else if (installing[pythonKey]?.status === 'Complete') {
          // Recently completed installations should count as 100%
          activeProgress += 100;
          activeInstallations++;
        }

        if (installing[modelsKey]?.status === 'Running') {
          activeProgress += installing[modelsKey].progress || 0;
          activeInstallations++;
        } else if (installing[modelsKey]?.status === 'Complete') {
          // Recently completed installations should count as 100%
          activeProgress += 100;
          activeInstallations++;
        }
      });

      if (totalComponents === 0) return 0;

      // Calculate base progress from already installed components
      const baseProgress = (completedComponents / totalComponents) * 100;

      // Add progress from active installations
      const activeContribution = activeInstallations > 0 ?
        (activeProgress / activeInstallations) * ((totalComponents - completedComponents) / totalComponents) : 0;

      return Math.min(100, baseProgress + activeContribution);
    };

    const moduleProgress = calculateModuleProgress();

    const colorClasses: { [key: string]: string } = {
      blue: 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20',
      green: 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20',
      purple: 'border-purple-200 bg-purple-50 dark:border-purple-800 dark:bg-purple-900/20',
      orange: 'border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-900/20',
      indigo: 'border-indigo-200 bg-indigo-50 dark:border-indigo-800 dark:bg-indigo-900/20',
      red: 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20',
      teal: 'border-teal-200 bg-teal-50 dark:border-teal-800 dark:bg-teal-900/20',
      gray: 'border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-800/20',
      pink: 'border-pink-200 bg-pink-50 dark:border-pink-800 dark:bg-pink-900/20'
    };

    const progressColorClasses: { [key: string]: string } = {
      blue: isDarkMode ? 'bg-blue-800' : 'bg-blue-300',
      green: isDarkMode ? 'bg-green-800' : 'bg-green-300',
      purple: isDarkMode ? 'bg-purple-800' : 'bg-purple-300',
      orange: isDarkMode ? 'bg-orange-800' : 'bg-orange-300',
      indigo: isDarkMode ? 'bg-indigo-800' : 'bg-indigo-300',
      red: isDarkMode ? 'bg-red-800' : 'bg-red-300',
      teal: isDarkMode ? 'bg-teal-800' : 'bg-teal-300',
      gray: isDarkMode ? 'bg-gray-700' : 'bg-gray-400',
      pink: isDarkMode ? 'bg-pink-800' : 'bg-pink-300'
    };

    return (
      <div key={module.id} className={`border rounded-lg ${colorClasses[module.color] || colorClasses.blue} ${isDarkMode ? 'border-gray-600' : 'border-gray-300'} relative overflow-hidden`}>
        {/* Module Header */}
        <div
          className="p-4 cursor-pointer hover:bg-opacity-80 transition-colors relative"
          onClick={() => toggleModule(module.id)}
        >
          {/* Progress Bar Overlay */}
          {(anyInstalling || allInstalled) && moduleProgress > 0 && (
            <div
              className={`absolute inset-0 ${progressColorClasses[module.color] || progressColorClasses.blue} transition-all duration-500 ease-out`}
              style={{
                width: `${moduleProgress}%`
              }}
            />
          )}

          {/* Content positioned above progress overlay */}
          <div className="flex items-center justify-between relative z-10">
            <div className="flex items-center gap-3">
              <div className={`text-${module.color}-600 dark:text-${module.color}-400`}>
                {module.icon}
              </div>
              <div>
                <h3 className="text-lg font-semibold">{module.name}</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">{module.description}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              {(() => {
                const overallStatus = getModuleOverallStatus(module);

                if (anyInstalling) {
                  return (
                    <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                      <Clock className="w-4 h-4 animate-spin" />
                      <span className="text-sm font-medium">Installing...</span>
                    </div>
                  );
                }

                // Show overall status indicator
                return (
                  <div className="flex items-center gap-2">
                    <div className={`flex items-center gap-1 px-2 py-1 rounded text-sm font-medium ${
                      overallStatus.status === 'satisfied'
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                        : overallStatus.status === 'attention'
                        ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                        : 'bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300'
                    }`}>
                      {overallStatus.status === 'satisfied' ? (
                        <CheckCircle className="w-3 h-3" />
                      ) : overallStatus.status === 'attention' ? (
                        <AlertTriangle className="w-3 h-3" />
                      ) : (
                        <Clock className="w-3 h-3" />
                      )}
                      <span>{overallStatus.message}</span>
                    </div>

                    {overallStatus.status !== 'satisfied' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Install all pipelines in this module
                          module.pipelines.forEach((p: PipelineStatus) => {
                            if (!p.python.installed) {
                              handleInstall(p.name, 'python', 'all');
                            }
                            if (!p.models.installed && p.python.installed && module.id !== 'core') {
                              handleInstall(p.name, 'models', 'all');
                            }
                          });
                        }}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
                      >
                        Install Module
                      </button>
                    )}
                  </div>
                );
              })()}
              {isExpanded ? (
                <ChevronDown className="w-5 h-5 text-gray-500" />
              ) : (
                <ChevronRight className="w-5 h-5 text-gray-500" />
              )}
            </div>
          </div>

          {/* Installed Indicator - Bottom Right Corner */}
          {allInstalled && (
            <div className="absolute bottom-2 right-2 flex items-center gap-1 text-xs font-medium text-green-600 dark:text-green-400 bg-white dark:bg-gray-800 px-2 py-1 rounded-full shadow-sm border border-green-200 dark:border-green-700">
              <CheckCircle className="w-3 h-3" />
              <span>Installed</span>
            </div>
          )}
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-600 p-4 space-y-4">
            {module.pipelines.map((pipeline: PipelineStatus) => (
              <div key={pipeline.name} className="space-y-3">
                <h4 className="font-medium text-gray-900 dark:text-gray-100">{pipeline.name}</h4>

                {/* Python Dependencies */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <Package className="w-4 h-4" /> Python Packages
                    </span>
                    {(() => {
                      const key = `${pipeline.name}-python-all`;
                      const isInstalling = installing[key]?.status === 'Running';
                      const persistentState = persistentStatus[key];

                      if (persistentState && !isInstalling) {
                        return (
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-1 rounded ${
                              persistentState.status === 'satisfied'
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                                : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                            }`}>
                              {persistentState.message}
                            </span>
                            <button
                              onClick={() => handleInstall(pipeline.name, 'python', 'all')}
                              className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded"
                            >
                              Reinstall
                            </button>
                          </div>
                        );
                      }

                      return (
                        <button
                          onClick={() => handleInstall(pipeline.name, 'python', 'all')}
                          disabled={isInstalling}
                          className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded disabled:bg-gray-400"
                        >
                          {pipeline.python.installed ? 'Reinstall' : 'Install'}
                        </button>
                      );
                    })()}
                  </div>
                  {renderPythonDetails(pipeline.name, pipeline.python.details)}
                  {installing[`${pipeline.name}-python-all`] && (
                    <ProgressBar progressData={installing[`${pipeline.name}-python-all`]} />
                  )}
                </div>

                {/* Models */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium flex items-center gap-2">
                      <HardDrive className="w-4 h-4" /> Models
                    </span>
                    {(() => {
                      const key = `${pipeline.name}-models-all`;
                      const isInstalling = installing[key]?.status === 'Running';
                      const persistentState = persistentStatus[key];

                      if (persistentState && !isInstalling) {
                        return (
                          <div className="flex items-center gap-2">
                            <span className={`text-xs px-2 py-1 rounded ${
                              persistentState.status === 'satisfied'
                                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300'
                                : 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300'
                            }`}>
                              {persistentState.message}
                            </span>
                            <button
                              onClick={() => handleInstall(pipeline.name, 'models', 'all')}
                              disabled={!pipeline.python.installed}
                              className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded disabled:bg-gray-400"
                              title={!pipeline.python.installed ? "Install Python packages first" : ""}
                            >
                              Reinstall
                            </button>
                          </div>
                        );
                      }

                      return (
                        <button
                          onClick={() => handleInstall(pipeline.name, 'models', 'all')}
                          disabled={!pipeline.python.installed || isInstalling}
                          className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded disabled:bg-gray-400"
                          title={!pipeline.python.installed ? "Install Python packages first" : ""}
                        >
                          {pipeline.models.installed ? 'Reinstall' : 'Install'}
                        </button>
                      );
                    })()}
                  </div>
                  {renderModelDetails(pipeline.name, pipeline.models.details, pipeline.python.installed)}
                  {installing[`${pipeline.name}-models-all`] && (
                    <ProgressBar progressData={installing[`${pipeline.name}-models-all`]} />
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderPythonDetails = (pipelineName: string, details: { [key: string]: DependencyDetail }) => {
    if (Object.keys(details).length === 0) {
      return <p className="text-xs text-gray-500 italic">No Python packages listed.</p>;
    }

    return (
      <ul className="space-y-1">
        {Object.entries(details).map(([_, detail]) => {
          const installKey = `${pipelineName}-python-${detail.required}`;
          const isInstalling = installing[installKey]?.status === 'Running';
          
          return (
            <li key={detail.required} className="text-xs px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded">
              <div className="flex items-center justify-between">
                <span className="font-mono">{detail.required}</span>
                {detail.satisfied ? (
                  <span className="flex items-center gap-1 text-green-500">
                    <CheckCircle className="w-3 h-3" /> {detail.installed}
                  </span>
                ) : (
                  <button
                    onClick={() => handleInstall(pipelineName, 'python', detail.required)}
                    disabled={isInstalling}
                    className="text-xs bg-blue-500 hover:bg-blue-600 text-white font-semibold px-2 py-1 rounded disabled:bg-gray-400"
                  >
                    {isInstalling ? 'Installing...' : 'Install'}
                  </button>
                )}
              </div>
              {installing[installKey] && (
                <ProgressBar progressData={installing[installKey]} />
              )}
            </li>
          );
        })}
      </ul>
    );
  };

  const renderModelDetails = (pipelineName: string, details: { [key: string]: ModelDetail }, pythonInstalled: boolean) => {
    if (Object.keys(details).length === 0) {
      return <p className="text-xs text-gray-500 italic">No models listed.</p>;
    }

    return (
      <ul className="space-y-1">
        {Object.entries(details).map(([_, model]) => {
          const installKey = `${pipelineName}-models-${model.name}`;
          const isInstalling = installing[installKey]?.status === 'Running';
          
          return (
            <li key={model.name} className="text-xs px-3 py-2 bg-gray-50 dark:bg-gray-700 rounded">
              <div className="flex items-center justify-between">
                <div>
                  <span className="font-semibold">{model.name}</span>
                  {model.required && (
                    <span className="ml-2 text-xs text-red-500">(Required)</span>
                  )}
                </div>
                {model.installed ? (
                  <span className="flex items-center gap-1 text-green-500">
                    <CheckCircle className="w-3 h-3" /> Installed
                  </span>
                ) : (
                  <button
                    onClick={() => handleInstall(pipelineName, 'models', model.name)}
                    disabled={isInstalling || !pythonInstalled}
                    className="text-xs bg-blue-500 hover:bg-blue-600 text-white font-semibold px-2 py-1 rounded disabled:bg-gray-400"
                    title={!pythonInstalled ? "Please install Python packages first" : `Install ${model.name}`}
                  >
                    {isInstalling ? 'Installing...' : 'Install'}
                  </button>
                )}
              </div>
              {installing[installKey] && (
                <ProgressBar progressData={installing[installKey]} />
              )}
            </li>
          );
        })}
      </ul>
    );
  };

  const ProgressBar = ({ progressData }: { progressData: InstallationProgress }) => (
    <div className="mt-2">
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 overflow-hidden">
            <div 
                className={`h-2.5 rounded-full transition-all duration-500 ease-out ${
                  progressData.status === 'Error' 
                    ? 'bg-red-500' 
                    : progressData.status === 'Complete' 
                      ? 'bg-green-500' 
                      : 'bg-blue-500'
                }`} 
                style={{ 
                  width: `${Math.max(progressData.progress, 2)}%` // Minimum 2% width for visibility
                }}
            ></div>
        </div>
        <p className={`text-xs mt-1 transition-colors duration-200 ${
          progressData.status === 'Error' 
            ? 'text-red-400' 
            : progressData.status === 'Complete'
              ? 'text-green-400'
              : 'text-gray-400'
        }`}>
            {progressData.status === 'Complete' ? '✓ ' : progressData.status === 'Error' ? '✗ ' : ''}
            {progressData.message}
        </p>
    </div>
  );

  return (
    <div className={`p-6 rounded-lg h-full flex flex-col ${isDarkMode ? 'bg-gray-800 text-white' : 'bg-white'}`}>
      <div className="flex items-center justify-start mb-6 flex-shrink-0">
        <h2 className="text-2xl font-bold flex items-center gap-2 mr-4">
          <Download className="w-6 h-6" />
          Dependency Manager
        </h2>
        <div className="flex gap-2">
          <button
            onClick={() => fetchStatus()}
            disabled={isLoading}
            className="p-2 rounded-lg transition-colors bg-blue-500 hover:bg-blue-600 text-white dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50 flex items-center gap-1"
            title="Refresh Dependency Status"
          >
            <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
            <span className="text-sm">Refresh</span>
          </button>
        </div>
      </div>

      {/* Overall Dependency Health Section */}
      {!isLoading && !error && (
        <div className="mb-6 p-4 border-2 border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Package className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100">Dependency Health</h3>
              </div>
              <div className="flex items-center gap-2">
                {overallHealth === 'Checking' ? (
                  <>
                    <Clock className="w-4 h-4 text-yellow-500" />
                    <span className="text-sm text-yellow-600 dark:text-yellow-400">Checking...</span>
                  </>
                ) : overallHealth === 'Good' ? (
                  <>
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span className="text-sm text-green-600 dark:text-green-400 font-medium">Good</span>
                  </>
                ) : (
                  <>
                    <AlertTriangle className="w-4 h-4 text-red-500" />
                    <span className="text-sm text-red-600 dark:text-red-400 font-medium">Poor</span>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="flex gap-3 mb-4">
            <button
              onClick={repairRequiredDependencies}
              disabled={fullInstallProgress.active}
              className="flex-1 bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-400 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              <RefreshCw className={`w-4 h-4 ${fullInstallProgress.active ? 'animate-spin' : ''}`} />
              Repair Required Dependencies
            </button>
            <button
              onClick={installFullDependencies}
              disabled={fullInstallProgress.active}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-2 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
            >
              <Download className={`w-4 h-4 ${fullInstallProgress.active ? 'animate-spin' : ''}`} />
              Install Full Dependencies
            </button>
          </div>

          {/* Full Install Progress Bar */}
          {fullInstallProgress.active && (
            <div className="mt-4">
              <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-3 overflow-hidden">
                <div
                  className="h-3 bg-blue-500 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${Math.max(fullInstallProgress.progress, 2)}%` }}
                ></div>
              </div>
              <p className="text-sm mt-2 text-blue-600 dark:text-blue-400">
                {fullInstallProgress.message}
              </p>
            </div>
          )}
        </div>
      )}

      {isLoading && (
        <div className="flex flex-col items-center py-10 gap-3">
          <span className="text-sm text-gray-500 dark:text-gray-300">Checking Dependencies…</span>
          <div className="w-56 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 overflow-hidden">
            <div
              className="h-2.5 bg-blue-500 rounded-full transition-all duration-300"
              style={{ width: `${progressPercent}%` }}
            ></div>
          </div>
        </div>
      )}
      {error && <div className="text-center py-10 text-red-500">{error}</div>}

      {!isLoading && !error && (
        <div className="space-y-6 overflow-y-auto pr-2 pb-8 flex-1">
          {getModuleConfig().map((module) => renderModule(module))}
        </div>
      )}
    </div>
  );
};

// Export wrapped component with error boundary
export const DependencyManagerView: React.FC<DependencyManagerViewProps> = (props) => (
  <DependencyErrorBoundary>
    <DependencyManagerViewInner {...props} />
  </DependencyErrorBoundary>
); 