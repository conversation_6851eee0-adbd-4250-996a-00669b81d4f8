basicsr-1.4.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
basicsr-1.4.2.dist-info/METADATA,sha256=1PoOWyGTRdJp84JcF5Xe0dLCSRqUD6ltkYQQf482838,11995
basicsr-1.4.2.dist-info/RECORD,,
basicsr-1.4.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
basicsr-1.4.2.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
basicsr-1.4.2.dist-info/licenses/LICENSE.txt,sha256=VEeHLyHdwp3A3AYNXIhVE8AckE6dqZMbummJim-01BU,11350
basicsr-1.4.2.dist-info/top_level.txt,sha256=pkLwIjnaUEmw2ccaiEbSWjcUsM9lugCWcEOZg_ROuNQ,8
basicsr/__init__.py,sha256=c5q4ESI1jZZ-jnWY-Azuy3pf3NJ1ezLi0ShYI1eWb0Y,286
basicsr/__pycache__/__init__.cpython-311.pyc,,
basicsr/__pycache__/test.cpython-311.pyc,,
basicsr/__pycache__/train.cpython-311.pyc,,
basicsr/__pycache__/version.cpython-311.pyc,,
basicsr/archs/__init__.py,sha256=S0u7qCf_4v8xuhGozLHzgTEv4htRN-YZn3xWoIkE85E,886
basicsr/archs/__pycache__/__init__.cpython-311.pyc,,
basicsr/archs/__pycache__/arch_util.cpython-311.pyc,,
basicsr/archs/__pycache__/basicvsr_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/basicvsrpp_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/dfdnet_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/dfdnet_util.cpython-311.pyc,,
basicsr/archs/__pycache__/discriminator_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/duf_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/ecbsr_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/edsr_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/edvr_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/hifacegan_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/hifacegan_util.cpython-311.pyc,,
basicsr/archs/__pycache__/inception.cpython-311.pyc,,
basicsr/archs/__pycache__/rcan_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/ridnet_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/rrdbnet_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/spynet_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/srresnet_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/srvgg_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/stylegan2_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/swinir_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/tof_arch.cpython-311.pyc,,
basicsr/archs/__pycache__/vgg_arch.cpython-311.pyc,,
basicsr/archs/arch_util.py,sha256=RbMiuHQFfEv_OCzI8bZFVIV7mqTgxH5z_mfkr8CyisE,11432
basicsr/archs/basicvsr_arch.py,sha256=qfuN9JzSpjk88Mtt-ZCiWEDgN0icheyUY7LmWokVwLo,12595
basicsr/archs/basicvsrpp_arch.py,sha256=OmS4_cpT6EnwnVmlQPW-FOVxWbJKdVUifI7DOA8SpKs,16570
basicsr/archs/dfdnet_arch.py,sha256=8Xx9at4_URkNs6EQMQp04WYZiHXE1-FJE0i48wV46sI,7464
basicsr/archs/dfdnet_util.py,sha256=hIhzmot0avo_TT2qv6bBf0QcHmIlixmkcU20kwBLrFw,5213
basicsr/archs/discriminator_arch.py,sha256=59QD08Ie3E4_9A3QuuWiBPwKQNSsBOZ2Nf7Yf_LtREU,6813
basicsr/archs/duf_arch.py,sha256=r6iR_j3HaSr1NpTwdibvQp7fvqU6ibyHt1o7Piey588,11825
basicsr/archs/ecbsr_arch.py,sha256=9KdcRuC6Yo3JuPR81Bv0px_czM7PEgbICOXq9r6pvQQ,11971
basicsr/archs/edsr_arch.py,sha256=AezDMY34ii3dYp-otr2Wpnv0W0a4uh0u1xUbQCoso20,2162
basicsr/archs/edvr_arch.py,sha256=LzTMKruLzIlQ78WbZJ5F1oZhsQ5wX8hassB_NbRXhEU,16244
basicsr/archs/hifacegan_arch.py,sha256=6ErwpBoLSkujxolaFFSvv0BFa3gA7-YVPlnUZsdJFzY,8932
basicsr/archs/hifacegan_util.py,sha256=sH2ZIhCFQ_XYuMuBuVEQzLJo0e_gkVrPYIuOXO5HjfA,9644
basicsr/archs/inception.py,sha256=qviu3GLfGd61OXAS-_5W8GATbnlAF2xf7dx8po54cYk,12055
basicsr/archs/rcan_arch.py,sha256=RbE7QwdUR-xgvKdRU3cnlorO-VslQUM00lwKFWLMzLI,4594
basicsr/archs/ridnet_arch.py,sha256=0iY28sdi3-03UJ1oHDK86bB-p7IDXk_-IQC8dzT35fk,6412
basicsr/archs/rrdbnet_arch.py,sha256=aqjrP3fpGLkO7_Tr-vh0Wr4MQOuPzAOrvBkoaT1wq1g,4621
basicsr/archs/spynet_arch.py,sha256=OJ0E1kkVMqigdGcncGuJ4iRCQ-jtQjkt7VhXuNfYg4M,3776
basicsr/archs/srresnet_arch.py,sha256=Gf8Thne62f2cD7duW_uGdjxl8rEb0MZ_aQVzEOwNst0,2665
basicsr/archs/srvgg_arch.py,sha256=_aYlR6Gry6aiftake4YWnuMcBQ7bXulNpDPFzYP3DcI,2739
basicsr/archs/stylegan2_arch.py,sha256=89kEac5ICLi9Lj24u6nXHhKks83Zv02ejOfNqewZotA,30194
basicsr/archs/swinir_arch.py,sha256=746sfW0hg8w_ppnQDKjdzA332y6vuIbiqrw3Lp8e25U,37271
basicsr/archs/tof_arch.py,sha256=F4IA8o7KLkcNbcHV9CfZcbRjRNHMTaBpFQ112eTdZzM,6226
basicsr/archs/vgg_arch.py,sha256=ueiE2hQXRVCxMhjDqJ-bq_Ih7eWjGODviZ9fjFpFB9M,6142
basicsr/data/__init__.py,sha256=tkfC3Pb-5qS9qiiU0qCU453Aysbhuq5Hnrujkxr4GYk,4332
basicsr/data/__pycache__/__init__.cpython-311.pyc,,
basicsr/data/__pycache__/data_sampler.cpython-311.pyc,,
basicsr/data/__pycache__/data_util.cpython-311.pyc,,
basicsr/data/__pycache__/degradations.cpython-311.pyc,,
basicsr/data/__pycache__/ffhq_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/paired_image_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/prefetch_dataloader.cpython-311.pyc,,
basicsr/data/__pycache__/realesrgan_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/realesrgan_paired_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/reds_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/single_image_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/transforms.cpython-311.pyc,,
basicsr/data/__pycache__/video_test_dataset.cpython-311.pyc,,
basicsr/data/__pycache__/vimeo90k_dataset.cpython-311.pyc,,
basicsr/data/data_sampler.py,sha256=Spx6tKLafEjM6Hxe6bICKnQApVn7N9iVtXl6Wwp1J8Y,1639
basicsr/data/data_util.py,sha256=td7QLFPJNUwl6d5euxr0eLp3nw8BwsrE2csejbwskzY,11800
basicsr/data/degradations.py,sha256=t442E3_45NeDd5te1hH8-AMFYzxJKsSU7lKyE4BvLYM,28346
basicsr/data/ffhq_dataset.py,sha256=itXAjy3QnEZ8Y6F1sdKPWzutplzit73ChKJNW-089Pc,3023
basicsr/data/paired_image_dataset.py,sha256=_plTYHpi6Wyoucv5oSifcDahC8wZzD3FCTQiGRpNPtQ,5024
basicsr/data/prefetch_dataloader.py,sha256=vm_1XbdaIpdnFt80zv5qatRe_t4L8-ktAn8-jZ6ZYAY,3131
basicsr/data/realesrgan_dataset.py,sha256=bSbN92pVBYNr6ZcACsHQKAgyU9VEgpA8JVsLI7Sn5EU,8750
basicsr/data/realesrgan_paired_dataset.py,sha256=VGQQAoq6hyMP3FOWrVNrdq024uZGWf7_1w30p90dyaA,5032
basicsr/data/reds_dataset.py,sha256=mmKenin9WxNmfUiDWs6S0wzaXBnXdB6QgnduifxHBNU,15329
basicsr/data/single_image_dataset.py,sha256=K-X-k03cDjFxkpUt72XdUvxUVkwjoskfrZSrs6enisU,2690
basicsr/data/transforms.py,sha256=hkF5teZhgL7xghKQndx_IKB903hMmIH_rhmE6nrpKU0,6225
basicsr/data/video_test_dataset.py,sha256=Vh5AAXnYhha8WVjAYcz9T6pVoH4HuIGNaqH0YCrAU3A,12110
basicsr/data/vimeo90k_dataset.py,sha256=iDE2BaCOo3SrJXcxjral_vySGRaYCqdMy9KfKqgE6Eg,6934
basicsr/losses/__init__.py,sha256=yxEFCniYpTQrhTu9890m4DPoda5tzvifcVXiqu2gSY8,1149
basicsr/losses/__pycache__/__init__.cpython-311.pyc,,
basicsr/losses/__pycache__/basic_loss.cpython-311.pyc,,
basicsr/losses/__pycache__/gan_loss.cpython-311.pyc,,
basicsr/losses/__pycache__/loss_util.cpython-311.pyc,,
basicsr/losses/basic_loss.py,sha256=XQHA5aOAtFrHCvjfxpn58WxYN5sfjuFEVSCc7sIB2x4,9245
basicsr/losses/gan_loss.py,sha256=m4gHFqJAKuKdjNidhBkY4Xylne1WYz8ihtJxnBAShsc,7488
basicsr/losses/loss_util.py,sha256=PC8UEW2c7zh1XqqY47ceOEtu8ykSlqMvSU0i_CxVCXI,4772
basicsr/metrics/__init__.py,sha256=39eCePKBO-NxWalCswp9AEi2bwRdvqT1C6HROAdIVVs,557
basicsr/metrics/__pycache__/__init__.cpython-311.pyc,,
basicsr/metrics/__pycache__/fid.cpython-311.pyc,,
basicsr/metrics/__pycache__/metric_util.cpython-311.pyc,,
basicsr/metrics/__pycache__/niqe.cpython-311.pyc,,
basicsr/metrics/__pycache__/psnr_ssim.cpython-311.pyc,,
basicsr/metrics/fid.py,sha256=vEansayZxeN-SpDEF24rB8l9HtUFCk9F9vzuc3yXnms,3255
basicsr/metrics/metric_util.py,sha256=NCVJBVLP-_DO9OK8yV2UD1q9CFWN_zxF43yROz3Orvw,1268
basicsr/metrics/niqe.py,sha256=eBE-by2CJ63MNRnPMFxjfjBbH8gmzG1TjK4cDmhpkSM,8352
basicsr/metrics/niqe_pris_params.npz,sha256=KnwYKmjJ5_Gy4uXscjJ51vZdkStvyvN-sr8D1zZ8QpY,11850
basicsr/metrics/psnr_ssim.py,sha256=G14ByMTbWy_VV-3oRcneMC1qjjYicFUc70BucHe_ZwY,8472
basicsr/models/__init__.py,sha256=nT1QHJHnGgPi_jyw_wvIKT_kYeSjjFYQMvM-0iejOJU,1011
basicsr/models/__pycache__/__init__.cpython-311.pyc,,
basicsr/models/__pycache__/base_model.cpython-311.pyc,,
basicsr/models/__pycache__/edvr_model.cpython-311.pyc,,
basicsr/models/__pycache__/esrgan_model.cpython-311.pyc,,
basicsr/models/__pycache__/hifacegan_model.cpython-311.pyc,,
basicsr/models/__pycache__/lr_scheduler.cpython-311.pyc,,
basicsr/models/__pycache__/realesrgan_model.cpython-311.pyc,,
basicsr/models/__pycache__/realesrnet_model.cpython-311.pyc,,
basicsr/models/__pycache__/sr_model.cpython-311.pyc,,
basicsr/models/__pycache__/srgan_model.cpython-311.pyc,,
basicsr/models/__pycache__/stylegan2_model.cpython-311.pyc,,
basicsr/models/__pycache__/swinir_model.cpython-311.pyc,,
basicsr/models/__pycache__/video_base_model.cpython-311.pyc,,
basicsr/models/__pycache__/video_gan_model.cpython-311.pyc,,
basicsr/models/__pycache__/video_recurrent_gan_model.cpython-311.pyc,,
basicsr/models/__pycache__/video_recurrent_model.cpython-311.pyc,,
basicsr/models/base_model.py,sha256=-eEw_e_jj1VANoU6CEnMnU_5QDmYiltAt1cwbvWnwXI,15222
basicsr/models/edvr_model.py,sha256=SJZggtwNRPfk69rMd7XBbZPllCv0zQGCLwwXQpDocH8,2429
basicsr/models/esrgan_model.py,sha256=CbY4xDACzKs01Qhxo2qSonKcXLYe4TTrh_PyKqNH0p8,3174
basicsr/models/hifacegan_model.py,sha256=ycYdq3L17bkfJsfX_Ri4Z7LFoX2Y8ySPvaQNjr3wGyo,11766
basicsr/models/lr_scheduler.py,sha256=RLVjn6HuihVigTTmU6MZMdlhxPllAIi7XsJstq5ATDQ,3956
basicsr/models/realesrgan_model.py,sha256=nsEcaLzfBP3e_7AI9bUn9pt0K8sUQhU-xWxGsD9cR5E,12261
basicsr/models/realesrnet_model.py,sha256=2oZaTYp8eO7A6J5J1lzp_naEHB84fRi53AjS6FsZu8E,9115
basicsr/models/sr_model.py,sha256=MAV0VOqnh0KwkleFHZRwSHkVVWwFIIU28b4LHe9q7xY,9310
basicsr/models/srgan_model.py,sha256=jH9yvXcVZiQhMZXvbqYlb0KocC_xM-Vx8vf4JlujDOM,5835
basicsr/models/stylegan2_model.py,sha256=lawzjiyGLKFGl0TTEGrToKhDpEMjY76LFCwjSW_1F9o,11642
basicsr/models/swinir_model.py,sha256=A2Vp1dxCju3dtWFLgAYpm7GUpM1GPqX6RMyiQyr_cT8,1115
basicsr/models/video_base_model.py,sha256=6nkW7KrL5QPvSxoaaHIOvtAdTVbGrkqekzYruROPCJ4,7433
basicsr/models/video_gan_model.py,sha256=FH3itOBuA8Mfm3uwXDdInz_hemtncAVls-ag9hp_ooQ,463
basicsr/models/video_recurrent_gan_model.py,sha256=ygN1zN_ofjHlszqZESnr_1yx3uZzHFnNp-TJdmjddRA,7142
basicsr/models/video_recurrent_model.py,sha256=rYHtCXBFF5ktuwdxMn7Jx9ihneVNg9yYMEHDmMAfYxo,8168
basicsr/ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
basicsr/ops/__pycache__/__init__.cpython-311.pyc,,
basicsr/ops/dcn/__init__.py,sha256=fjqFjNN6ORHQJWUOXPCOoucRvZkIkWmi0DD5RqMqle8,306
basicsr/ops/dcn/__pycache__/__init__.cpython-311.pyc,,
basicsr/ops/dcn/__pycache__/deform_conv.cpython-311.pyc,,
basicsr/ops/dcn/deform_conv.py,sha256=dYfC83l33SbW8WheG2J2bhHIek8QRLjMVc4Da_-W2HA,15724
basicsr/ops/dcn/src/deform_conv_cuda.cpp,sha256=5JaS7YUgBz0w-XDYFkffzKjSComleHC08rxEFtXe71k,28842
basicsr/ops/dcn/src/deform_conv_cuda_kernel.cu,sha256=QyXAo-a6fF8nzmodGKLTpmlJp-PEC8kNh9CLmZFXqZY,42622
basicsr/ops/dcn/src/deform_conv_ext.cpp,sha256=PPnOCq7mt6asRoPwbU0ENYlWDvVwmAlBQM-lMxftJfw,7492
basicsr/ops/fused_act/__init__.py,sha256=Zf2DYfnTm_d9hHwVfsg4vsbSCnjV9PoPRlEt-qoQz7A,106
basicsr/ops/fused_act/__pycache__/__init__.cpython-311.pyc,,
basicsr/ops/fused_act/__pycache__/fused_act.cpython-311.pyc,,
basicsr/ops/fused_act/fused_act.py,sha256=jJRMUavPbfIOMhBUfOSCrsIeX06AMySKfLb4kNRRCR0,2941
basicsr/ops/fused_act/src/fused_bias_act.cpp,sha256=rwH9TIbpNKFkpO6fYUrjyFWb4OaCK79UK3zWxby1OiQ,1092
basicsr/ops/fused_act/src/fused_bias_act_kernel.cu,sha256=k9S46YMDyDThpItdpeakMOb5iFOzG_H68-eqYvXOLbg,2874
basicsr/ops/upfirdn2d/__init__.py,sha256=QejkM31gix1edmnyGB5QzSM4V5bfkLCsF4_AJyIaSyM,58
basicsr/ops/upfirdn2d/__pycache__/__init__.cpython-311.pyc,,
basicsr/ops/upfirdn2d/__pycache__/upfirdn2d.cpython-311.pyc,,
basicsr/ops/upfirdn2d/src/upfirdn2d.cpp,sha256=I4eoxbX3MjDGehO9QQe4t0B4CfP8ZJAhRfJJui2n7AI,1052
basicsr/ops/upfirdn2d/src/upfirdn2d_kernel.cu,sha256=mz53pD5uvhLG1gV-vkmlBS7lc5zV_HkV7yFvuEsW26g,11803
basicsr/ops/upfirdn2d/upfirdn2d.py,sha256=9kJO7ipsYupvOm51TaHZTSLsvp7ktPYGSrd4OcFrSI4,6085
basicsr/test.py,sha256=MazzUtnEkOJeNAGIq8CbSSdF9bfcbQ4LDtjaA3yTLPM,1730
basicsr/train.py,sha256=lSM9kwuc4O1c3r9VOaBUzeKOmbGJ6zJbUnj0J_ZbnG8,9672
basicsr/utils/__init__.py,sha256=XcvfFaV5m-_x8_jdGs6OZgllPj0yAwujgiKTTVLEicE,1158
basicsr/utils/__pycache__/__init__.cpython-311.pyc,,
basicsr/utils/__pycache__/color_util.cpython-311.pyc,,
basicsr/utils/__pycache__/diffjpeg.cpython-311.pyc,,
basicsr/utils/__pycache__/dist_util.cpython-311.pyc,,
basicsr/utils/__pycache__/download_util.cpython-311.pyc,,
basicsr/utils/__pycache__/file_client.cpython-311.pyc,,
basicsr/utils/__pycache__/flow_util.cpython-311.pyc,,
basicsr/utils/__pycache__/img_process_util.cpython-311.pyc,,
basicsr/utils/__pycache__/img_util.cpython-311.pyc,,
basicsr/utils/__pycache__/lmdb_util.cpython-311.pyc,,
basicsr/utils/__pycache__/logger.cpython-311.pyc,,
basicsr/utils/__pycache__/matlab_functions.cpython-311.pyc,,
basicsr/utils/__pycache__/misc.cpython-311.pyc,,
basicsr/utils/__pycache__/options.cpython-311.pyc,,
basicsr/utils/__pycache__/plot_util.cpython-311.pyc,,
basicsr/utils/__pycache__/registry.cpython-311.pyc,,
basicsr/utils/color_util.py,sha256=SF80oh1XlVPMwCX0GD3aTtUZ7VgkP-hra0MFabqiYxk,7981
basicsr/utils/diffjpeg.py,sha256=rCDMF8KWhLJ88IoNDBKtau44EakzPM_DpF3ZbSm4u78,15666
basicsr/utils/dist_util.py,sha256=5qbX_O1RRv8uHxbLDOHJIhcf5-iyjAEReGlevtT0d7g,2608
basicsr/utils/download_util.py,sha256=M2A-u4zxg0jggwUUYz4zajv0dJoUSoZHIFn38Lg7K1A,3345
basicsr/utils/file_client.py,sha256=OOrYz67ebBnDJi5F-9dsW6YyR5tihjE120JYkyghFp0,6014
basicsr/utils/flow_util.py,sha256=kO5gnJxOAOg4NgQYLqFc2m9aa6BrsZ8DGONI97_4WzU,6159
basicsr/utils/img_process_util.py,sha256=6S8-4QK8o7fdjywrGWpAwVx80YHHgkVexOcfQzHM1iU,2563
basicsr/utils/img_util.py,sha256=Q1XrEdmV7gH3psb4jf1BAlNgNIsREF1wwRKI7Kkm9Tc,6195
basicsr/utils/lmdb_util.py,sha256=I3S5v9BoE6kcq1T4XinGpgbfIEUUM6qt616EewlXdEk,7123
basicsr/utils/logger.py,sha256=BYJXcdTRkJ092lyAXwespx7wy-KbHFAX0iXhya9r8Xc,7150
basicsr/utils/matlab_functions.py,sha256=KaOj0gnOFXJCAr-wFBXl1OV057hTCQVRp5OMe3jsSXU,6962
basicsr/utils/misc.py,sha256=bsGRk2f_CBIhi3QBhnHURctOxarMW_WDzCrXq-PGrQE,4655
basicsr/utils/options.py,sha256=RaM2HHWoqzEENSW7gNAXoodXZza5X_gMgVuaXTOvxFk,6406
basicsr/utils/plot_util.py,sha256=z63ghu1kdnBEGkqaonkYLFiAufxxkKjsOx0CTbKRORE,2515
basicsr/utils/registry.py,sha256=a0cFsBf-RYnSlgNZLe-mv1jHDIk2Zm2cgt6EhbLk0LU,2477
basicsr/version.py,sha256=Vwcd7fnJgid-wx7b0iCZ6R1ZQDbJNr6b2LJBD9XUOQ8,133
