{
  "name": "ImageGeneration",
  "description": "Advanced image generation using multiple Stable Diffusion models",
  "dependencies": {
    "python": [
      "torch>=2.7.1+cu128",
      "torchvision>=0.18.1+cu128",
      "diffusers>=0.25.0",
      "transformers>=4.36.2",
      "accelerate>=0.26.1",
      "bitsandbytes>=0.37.0",
      "safetensors>=0.4.1",
      "xformers>=0.0.23.post1",
      "optimum-quanto>=0.2.0",
      "einops>=0.8.0",
      "compel>=2.0.0",
      "opencv-python>=********",
      "pillow>=10.0.0",
      "huggingface_hub>=0.20.3",
      "hf_transfer>=0.1.4",
      "numpy>=1.24.3",
      "matplotlib>=3.7.1",
      "protobuf>=3.20.0",
      "sentencepiece>=0.1.97",
scipy>=1.10.0
    ],
    "models": [
      {
        "name": "sdxl-turbo",
        "repo_id": "stabilityai/sdxl-turbo",
        "required": true,
        "description": "SDXL Turbo - Ultra-fast 1-step generation",
        "local_path": "ImageGeneration/sdxl-turbo"
      },
      {
        "name": "stable-diffusion-xl-base-1.0",
        "repo_id": "stabilityai/stable-diffusion-xl-base-1.0",
        "required": false,
        "description": "SDXL Base 1.0 - High quality generation",
        "local_path": "ImageGeneration/stable-diffusion-xl-base-1.0"
      },
      {
        "name": "stable-diffusion-xl-refiner-1.0",
        "repo_id": "stabilityai/stable-diffusion-xl-refiner-1.0",
        "required": false,
        "description": "SDXL Refiner 1.0 - Quality enhancement",
        "local_path": "ImageGeneration/stable-diffusion-xl-refiner-1.0"
      },
      {
        "name": "flux-dev",
        "repo_id": "black-forest-labs/FLUX.1-dev",
        "required": false,
        "description": "FLUX Dev - High quality advanced generation with guidance scale support",
        "local_path": "ImageGeneration/fluxDev"
      },
      {
        "name": "stable-diffusion-v1-5",
        "repo_id": "runwayml/stable-diffusion-v1-5",
        "required": false,
        "description": "Stable Diffusion v1.5 - Classic model",
        "local_path": "ImageGeneration/stable-diffusion-v1-5"
      },
      {
        "name": "stable-diffusion-2-1",
        "repo_id": "stabilityai/stable-diffusion-2-1",
        "required": false,
        "description": "Stable Diffusion v2.1 - Improved model",
        "local_path": "ImageGeneration/stable-diffusion-2-1"
      }
    ]
  }
}